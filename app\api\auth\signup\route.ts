// Endpoint Supabase signup – TODO: proteggere con middleware JWT
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const signupSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6)
});

export async function POST(req: NextRequest) {
  let body: unknown;
  try {
    body = await req.json();
  } catch {
    return NextResponse.json({ error: 'JSON malformato' }, { status: 400 });
  }
  const result = signupSchema.safeParse(body);
  if (!result.success) {
    return NextResponse.json({ error: 'Validazione fallita', details: result.error.errors }, { status: 422 });
  }
  const { email, password } = result.data;
  const { data, error } = await supabase.auth.admin.createUser({
    email,
    password,
    email_confirm: true
  });
  if (error) {
    if (error.message && error.message.includes('already registered')) {
      return NextResponse.json({ error: 'Email already registered' }, { status: 409 });
    }
    return NextResponse.json({ error: error.message }, { status: 400 });
  }
  return NextResponse.json({ user: data.user }, { status: 201 });
}
