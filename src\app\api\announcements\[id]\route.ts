console.info("🔄 Announcements proxy route loaded");
// 📄 src/app/api/announcements/[id]/route.ts
// Edge-compatible handler for announcement detail (GET, PUT, DELETE stubs)
import { NextRequest, NextResponse } from "next/server";

const BACKEND = process.env.BACKEND_URL!; // e.g. http://localhost:8000

async function proxy(req: NextRequest, id: string) {
  const url = `${BACKEND}/api/announcements/${id}`;
  if (!process.env.BACKEND_URL) {
    console.error("❌ BACKEND_URL env var missing");
    return NextResponse.json({ error: "BACKEND_URL not set" }, { status: 500 });
  }
  console.log("➡️  Proxy GET", id, "→", url);
  const init: RequestInit = {
    method: req.method,
    headers: {
      "Content-Type": "application/json",
      // Propagate JWT if present
      Authorization: req.headers.get("authorization") ?? "",
    },
    body: req.method !== "GET" ? await req.text() : undefined,
  };

  const res = await fetch(url, init);
  console.log("⬅️  Proxy status", res.status);
  const data = await res.text(); // keep as text to forward raw JSON/errors
  return NextResponse.json(data ? JSON.parse(data) : null, { status: res.status });
}

// GET ➜ fetch single announcement
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    return await proxy(req, params.id);
  } catch (e) {
    return NextResponse.json({ error: e instanceof Error ? e.message : "Unknown error" }, { status: 500 });
  }
}

// PUT, DELETE stubs for future use
export async function PUT() {
  return NextResponse.json({ message: "Not implemented" }, { status: 501 });
}
export async function DELETE() {
  return NextResponse.json({ message: "Not implemented" }, { status: 501 });
}
