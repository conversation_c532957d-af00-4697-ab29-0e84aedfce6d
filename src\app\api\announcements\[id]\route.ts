// 📄 src/app/api/announcements/[id]/route.ts
// API route per dettaglio singolo annuncio - accesso diretto al database
import { NextRequest, NextResponse } from "next/server";
import { query } from '@/lib/db';

// GET ➜ fetch single announcement
export async function GET(_req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;

    // Validazione ID
    const announcementId = parseInt(id);
    if (isNaN(announcementId)) {
      return NextResponse.json({ error: 'ID annuncio non valido' }, { status: 400 });
    }

    // Query per ottenere il dettaglio dell'annuncio
    const rows = await query(
      `SELECT id,
              title,
              description,
              announcement_type AS type,
              region,
              location,
              budget_min,
              budget_max,
              salary_range,
              availability,
              created_at
       FROM announcements
       WHERE id = $1`,
      [announcementId]
    );

    if (rows.length === 0) {
      return NextResponse.json({ error: '<PERSON>uncio non trovato' }, { status: 404 });
    }

    return NextResponse.json(rows[0]);
  } catch (error) {
    console.error('Errore nel recupero annuncio:', error);
    return NextResponse.json({ error: 'Errore interno del server' }, { status: 500 });
  }
}

// PUT e DELETE saranno implementati in futuro se necessario
