
import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Rebuild Link App",
  description: "Rebuild Link Application",
};

// Layout semplificato per debug - rimuoviamo temporaneamente LocaleProvider e font
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">
        <div>
          <p>Layout caricato correttamente</p>
          {children}
        </div>
      </body>
    </html>
  );
}
