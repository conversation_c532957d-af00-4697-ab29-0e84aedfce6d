# ReBuild Link

Marketplace multi‑tenant per la ricostruzione in contesti complessi (prima istanza: **Ucraina**). Piattaforma web + API + asset dati.

---

## 🌐 Visione

Un unico hub dove – in qualunque area post‑crisi – aziende internazionali possono:

- trovare fornitori di mezzi, materiali, logistica
- reclutare personale locale/estero (job request)
- accedere a report di mercato basati sui dati generati.

Il codice è **riutilizzabile**: ogni territorio è un _tenant_ isolato tramite `tenant_id`.

---

## 🔧 Stack Tecnologico

| Layer                    | Scelte                                                    |
| ------------------------ | --------------------------------------------------------- |
| **Frontend**             | Next.js 15 (App Router) · React · TypeScript · Tailwind 3 |
| **Backend (API Routes)** | Node (Next API) · pg Pool                                 |
| **Auth / Storage**       | Supabase (GoTrue, Storage)                                |
| **Database**             | PostgreSQL 15 (Docker → AWS RDS in prod)                  |
| **CI/CD**                | GitHub Actions → Vercel (frontend)                        |
| **Dev tools**            | VS Code + Copilot                                         |

---

## 📚 Schema dati essenziale

```
tenants  (id, slug, name, default_locale, ...)
users    (id, tenant_id, user_type, ...)
announcements (id, tenant_id, creator_id, type, ...)
announcement_categories (announcement_id, category_id)
messages, ratings, event_logs ...
```

Multi‑tenancy via colonna `tenant_id` + middleware che legge lo slug URL.

---

## 🛣️ API (MVP)

| Verbo    | Rotta                    | Descrizione                        |
| -------- | ------------------------ | ---------------------------------- |
| **GET**  | `/api/announcements`     | Lista ultimi 20 annunci del tenant |
| **POST** | `/api/announcements`     | Crea nuovo annuncio _(auth)_       |
| **GET**  | `/api/announcements/:id` | Dettaglio singolo annuncio         |
| **POST** | `/api/auth/signup`       | Registrazione Supabase             |
| **POST** | `/api/auth/login`        | Login Supabase                     |

Validazione payload tramite `src/lib/validators.ts` (Zod). Auth protetta da middleware JWT.

---

## 🚀 Avvio locale

```bash
# 1. Clona repo e installa dipendenze
cd app && npm install

# 2. Avvia Postgres in Docker
cd ..
docker run --name rebuild-postgres -e POSTGRES_PASSWORD=pass123 -e POSTGRES_DB=rebuildlink -p 5432:5432 -d postgres:15

# 3. Migrazione schema
psql "postgresql://postgres:pass123@localhost:5432/rebuildlink" -f db/migrations/001_schema.sql

# 4. Seed demo (categorie + annunci)
docker cp db/seed.sql rebuild-postgres:/tmp/seed.sql
docker exec -it rebuild-postgres psql -U postgres -d rebuildlink -f /tmp/seed.sql

# 5. Variabili ambiente
cp app/.env.local.example app/.env.local  # poi inserisci DATABASE_URL + Supabase keys

# 6. Dev server
cd app && npm run dev  # http://localhost:3000/ua
```

---

## 🗂 Architettura cartelle (app/)

```
app/
 ├─ src/
 │   ├─ app/               # App Router pages & layouts
 │   ├─ components/        # UI riutilizzabili
 │   ├─ lib/               # db.ts • validators.ts • auth helpers
 │   ├─ types/             # d.ts condivisi
 │   └─ test/              # unit / integration
 ├─ public/                # asset statici
 └─ .env.local             # segreti (git‑ignored)
```

---

## 🏁 Roadmap (Q3 2025)

| Fase          | Obiettivo                       | Stato |
| ------------- | ------------------------------- | ----- |
| **MVP 1**     | Home + lista annunci (GET)      | ✅    |
| **MVP 2**     | POST /announcements + Form FE   | 🔜    |
| **MVP 3**     | Auth UI + route protette        | ◻︎    |
| **MVP 4**     | Chat realtime (Supabase)        | ◻︎    |
| **CI/CD**     | GitHub Actions + Vercel preview | ◻︎    |
| **Analytics** | Dashboard trend / report export | ◻︎    |

---

## 🤝 Contribuire

1. Apri una issue o un thread dedicato (#frontend, #backend, ecc.).
2. Mantieni PR piccole; un task → un commit.
3. Format: `npm run lint` prima del push.

---

**Let’s rebuild, together.**
