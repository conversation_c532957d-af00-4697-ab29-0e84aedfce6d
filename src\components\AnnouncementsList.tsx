"use client";
import useSWR from 'swr';

type Announcement = {
  id: number;
  title: string;
  type: 'project' | 'job';
  region?: string;
  location?: string;
};


const fetcher = () => {
  // O<PERSON>eni lo slug della lingua dalla path corrente (es: '/ua')
  const slug = typeof window !== 'undefined' ? window.location.pathname.split('/')[1] : '';
  const url = `/${slug}/api/announcements`;
  return fetch(url).then(r => r.json());
};

export default function AnnouncementsList() {
  const { data, error, isLoading } = useSWR<Announcement[]>(null, fetcher);

  if (isLoading) return <div>Caricamento…</div>;
  if (error) return <div>Errore nel caricamento degli annunci.</div>;
  if (!data || !Array.isArray(data)) return <p>Nessun annuncio.</p>;

  return (
    <ul className="space-y-4">
      {data.map((a: Announcement) => (
        <li
          key={a.id}
          className="border border-neutral-700 p-4 rounded-lg hover:bg-neutral-800"
        >
          <p className="font-semibold">{a.title}</p>
          <p className="text-sm text-neutral-400">
            {a.type === 'project' ? `Regione: ${a.region}` : `Luogo: ${a.location}`}
          </p>
        </li>
      ))}
    </ul>
  );
}
